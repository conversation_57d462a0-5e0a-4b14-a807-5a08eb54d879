//
//  AppSettings.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import SwiftUI
import Foundation

// MARK: - 用户设置数据模型
struct UserSetting: Codable, Equatable {
    var userId: String = "0224"
    var nickname: String = "新用户"
    var avatarData: Data? = nil
    var firstUsageDate: Date?
    var appUsageDays: Int = 0 // 初始化的日期
    var selectedAppIcon: String = "AppIcon" // 默认图标名称
    var geminiApiKey: String = "" // Gemini API密钥

    // MARK: - 头像管理

    /// 是否有自定义头像
    var hasCustomAvatar: Bool {
        avatarData != nil
    }

    /// 设置为系统默认头像
    mutating func setSystemDefaultAvatar() {
        self.avatarData = nil
    }

    /// 从 UIImage 设置自定义头像（自动压缩）
    /// - Parameter image: 传入的 UIImage，nil 表示清除
    mutating func setCustomAvatar(from image: UIImage?) {
        guard let image = image else {
            self.avatarData = nil
            return
        }

        // 压缩图像到合理尺寸（如 300x300）
        let maxSize: CGFloat = 300.0
        let resized = image.resized(to: CGSize(width: maxSize, height: maxSize))
        self.avatarData = resized.jpegData(compressionQuality: 0.8)
        
    }

    /// 获取头像 UIImage
    func getAvatarImage() -> UIImage? {
        if let data = avatarData {
            return UIImage(data: data)
        }
        return nil
    }

    /// 获取 SwiftUI Image（带 fallback）
    func getAvatarSwiftUIImage() -> Image {
        if let uiImage = getAvatarImage() {
            return Image(uiImage: uiImage)
        }
        return Image(systemName: "person.circle.fill")
    }
}

// MARK: - UIImage 扩展（用于压缩）
private extension UIImage {
    func resized(to size: CGSize) -> UIImage {
        UIGraphicsImageRenderer(size: size).image { _ in
            self.draw(in: CGRect(origin: .zero, size: size))
        }
    }
}

// MARK: - App设置和数据管理
@MainActor
final class AppSettings: ObservableObject {

    /// 使用 @AppStorage 存储编码后的 UserSetting 数据
    @AppStorage("userSettingsData") private var _userSettingsData: Data?

    /// 用于记录最后一次使用日期
    @AppStorage("lastUsageDate") private var lastUsageDate: Date?

    /// 获取当前登录用户ID
    @AppStorage("currentUserId") private var currentUserId: String = ""

    /// 实际被视图绑定的设置实例
    @Published var settings: UserSetting = UserSetting(){
        didSet{
            if let encoded = try? JSONEncoder().encode(settings) {
                _userSettingsData = encoded
            }
            // 当设置更新时，同步到云端
            Task {
                await syncUserInfoToCloud()
            }
        }
    }

    private let encoder = JSONEncoder()
    private let decoder = JSONDecoder()
    private let userInfoService = UserInfoUpdateService.shared
    private let itemCardManager = ItemCardManager()

    init() {
        loadSettings()
        updateUsageDays()
        setupNotificationObservers()

        // 检查用户登录状态并自动同步
        Task {
            await checkAndAutoSync()
        }
    }

    /// 检查用户登录状态并自动同步
    private func checkAndAutoSync() async {
        // 延迟一点时间，确保app完全启动
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1秒

        guard !currentUserId.isEmpty else {
            print("ℹ️ 用户未登录，跳过自动同步")
            return
        }

        print("🔄 检测到用户已登录 (ID: \(currentUserId))，开始自动同步...")
        await loadUserInfoFromCloud()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: 统一保存方法
    func save() {
        if let encoded = try? encoder.encode(settings) {
            _userSettingsData = encoded
        }
    }
    
    func updateSettings(_ update: (inout UserSetting) -> Void) {
        var newSettings = settings
        update(&newSettings)
        settings = newSettings // 触发 didSet
    }
    
    // MARK: 使用计算属性便捷访问
    var nickname: String {
        get { settings.nickname }
        set {
            updateSettings { $0.nickname = newValue }
            // 昵称更新时同步到云端
            Task {
                await syncNicknameToCloud(newValue)
            }
        }
    }

    var userId: String {
        get { settings.userId }
        set { updateSettings { $0.userId = newValue } }
    }

    var geminiApiKey: String {
        get { settings.geminiApiKey }
        set { updateSettings { $0.geminiApiKey = newValue } }
    }

    // MARK: - 头像管理（带云端同步）

    /// 设置自定义头像并同步到云端
    func setCustomAvatarWithSync(from image: UIImage?) async {
        // 先更新本地设置
        updateSettings { settings in
            settings.setCustomAvatar(from: image)
        }

        // 如果有图片，上传到云端并更新用户信息
        if let image = image,
           let imageData = image.jpegData(compressionQuality: 0.8),
           !currentUserId.isEmpty {
            do {
                _ = try await userInfoService.uploadAvatarAndUpdateUser(
                    userId: currentUserId,
                    imageData: imageData
                )
                print("✅ 头像已同步到云端")
            } catch {
                print("❌ 头像同步到云端失败: \(error.localizedDescription)")
            }
        }
    }

    // MARK: - 私有方法
    private func loadSettings() {
        guard let data = _userSettingsData,
              let decoded = try? decoder.decode(UserSetting.self, from: data)
        else { return }
        self.settings = decoded
    }

    // MARK: - 云端同步方法

    /// 同步用户信息到云端
    private func syncUserInfoToCloud() async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过云端同步")
            return
        }

        do {
            _ = try await userInfoService.updateUserInfo(
                userId: currentUserId,
                nickname: settings.nickname
            )
            print("✅ 用户信息已同步到云端")
        } catch {
            print("❌ 用户信息同步到云端失败: \(error.localizedDescription)")
        }
    }

    /// 同步昵称到云端
    private func syncNicknameToCloud(_ nickname: String) async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过昵称同步")
            return
        }

        do {
            _ = try await userInfoService.updateNickname(
                userId: currentUserId,
                nickname: nickname
            )
            print("✅ 昵称已同步到云端: \(nickname)")
        } catch {
            print("❌ 昵称同步到云端失败: \(error.localizedDescription)")
        }
    }

    /// 从云端加载用户信息
    func loadUserInfoFromCloud() async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过云端加载")
            return
        }

        do {
            let userData = try await userInfoService.getUserInfo(userId: currentUserId)

            // 更新本地设置（不触发云端同步）
            await MainActor.run {
                var newSettings = settings
                newSettings.nickname = userData.nickname
                newSettings.userId = userData.userId

                // 直接更新设置，避免触发didSet中的云端同步
                if let encoded = try? encoder.encode(newSettings) {
                    _userSettingsData = encoded
                    settings = newSettings
                }
            }

            // 如果有头像URL，下载并保存到本地
            if let avatarURL = userData.avatarURL, !avatarURL.isEmpty {
                await downloadAndSaveAvatar(from: avatarURL)
            }

            // 同步用户持有的ItemCard信息
            await syncUserItemCardsFromCloud()

            print("✅ 用户信息已从云端加载: \(userData.nickname)")
        } catch {
            print("❌ 从云端加载用户信息失败: \(error.localizedDescription)")
        }
    }

    /// 从URL下载头像并保存到本地沙盒
    private func downloadAndSaveAvatar(from urlString: String) async {
        guard let url = URL(string: urlString) else {
            print("❌ 无效的头像URL: \(urlString)")
            return
        }

        // 检查是否需要下载（避免重复下载相同的头像）
        let lastAvatarURL = UserDefaults.standard.string(forKey: "lastAvatarURL")
        if lastAvatarURL == urlString && settings.hasCustomAvatar {
            print("ℹ️ 头像URL未变化且本地已有头像，跳过下载")
            return
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            // 检查响应状态
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200,
               let image = UIImage(data: data) {

                // 在主线程更新UI
                await MainActor.run {
                    var newSettings = settings
                    newSettings.setCustomAvatar(from: image)

                    // 直接更新设置，避免触发didSet中的云端同步
                    if let encoded = try? encoder.encode(newSettings) {
                        _userSettingsData = encoded
                        settings = newSettings
                    }

                    // 保存当前头像URL，避免重复下载
                    UserDefaults.standard.set(urlString, forKey: "lastAvatarURL")
                }

                print("✅ 头像已从云端下载并保存到本地")
            } else {
                print("❌ 头像下载失败，HTTP状态码异常")
            }
        } catch {
            print("❌ 头像下载失败: \(error.localizedDescription)")
        }
    }

    /// 从云端同步用户持有的ItemCard信息
    private func syncUserItemCardsFromCloud() async {
        guard !currentUserId.isEmpty else {
            print("⚠️ 用户未登录，跳过ItemCard同步")
            return
        }

        do {
            // 获取用户持有的所有卡片
            let userItemCards = try await itemCardManager.getUserItemCards(userId: currentUserId)
            print("📦 从云端获取到 \(userItemCards.count) 张用户持有的卡片")

            // 提取卡片信息并保存到本地ItemCardViewModel
            var cardsToStore: [ItemCard] = []

            // 为每张卡片下载图片到本地
            for userItemCard in userItemCards {
                guard let card = userItemCard.card else { continue }

                // 检查本地是否已有该图片文件
                let localImagePath = getDocumentsDirectory().appendingPathComponent(card.imageFileName)
                if FileManager.default.fileExists(atPath: localImagePath.path) {
                    print("ℹ️ 卡片图片已存在本地: \(card.imageFileName)")
                } else {
                    // 从COS下载图片到本地
                    await downloadItemCardImage(imageURL: card.imageURL, fileName: card.imageFileName)
                }

                // 添加到待存储列表
                cardsToStore.append(card)
            }

            // 通知CardStore更新卡片列表
            await MainActor.run {
                NotificationCenter.default.post(
                    name: NSNotification.Name("UserItemCardsDidSync"),
                    object: cardsToStore
                )
            }

            print("✅ ItemCard云端同步完成，已通知CardStore更新")
        } catch {
            print("❌ ItemCard云端同步失败: \(error.localizedDescription)")
        }
    }

    /// 下载ItemCard图片到本地Documents目录
    private func downloadItemCardImage(imageURL: String, fileName: String) async {
        guard let url = URL(string: imageURL) else {
            print("❌ 无效的图片URL: \(imageURL)")
            return
        }

        do {
            let (data, response) = try await URLSession.shared.data(from: url)

            // 检查响应状态
            if let httpResponse = response as? HTTPURLResponse,
               httpResponse.statusCode == 200 {

                // 保存图片到Documents目录
                let localURL = getDocumentsDirectory().appendingPathComponent(fileName)
                try data.write(to: localURL)

                print("✅ 卡片图片已下载到本地: \(fileName)")
            } else {
                print("❌ 卡片图片下载失败，HTTP状态码异常: \(imageURL)")
            }
        } catch {
            print("❌ 卡片图片下载失败: \(error.localizedDescription)")
        }
    }

    /// 获取Documents目录路径
    private func getDocumentsDirectory() -> URL {
        return FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
    }

    private func updateUsageDays() {
        let now = Date()
        let calendar = Calendar.current

        guard let firstDate = settings.firstUsageDate else {
            // 初始化：记录今天为第一天，并将总天数设为 1
            updateSettings { settings in
                settings.firstUsageDate = now
                settings.appUsageDays = 1
            }
            return
            }

        let daysDifference = calendar.dateComponents([.day], from: firstDate, to: now).day ?? 0

        settings.appUsageDays = daysDifference + 1

        updateSettings { settings in
            settings.appUsageDays = daysDifference + 1
        }
    }



    // MARK: - 公共同步方法

    /// 手动触发完整的用户信息同步
    func syncAllUserInfoToCloud() async {
        await syncUserInfoToCloud()
    }

    /// 在用户登录后调用，从云端加载用户信息
    func onUserLogin(userId: String) async {
        currentUserId = userId
        await loadUserInfoFromCloud()
        // loadUserInfoFromCloud 中已经包含了 syncUserItemCardsFromCloud 调用
    }

    /// 在用户登录后调用，直接使用已获取的用户数据
    func onUserLoginWithData(userId: String, userData: UserData) async {
        currentUserId = userId

        // 直接更新本地设置（不触发云端同步）
        await MainActor.run {
            var newSettings = settings
            newSettings.nickname = userData.nickname
            newSettings.userId = userData.userId

            // 直接更新设置，避免触发didSet中的云端同步
            if let encoded = try? encoder.encode(newSettings) {
                _userSettingsData = encoded
                settings = newSettings
            }
        }

        // 如果有头像URL，立即下载并保存到本地
        if let avatarURL = userData.avatarURL, !avatarURL.isEmpty {
            await downloadAndSaveAvatar(from: avatarURL)
        }

        // 同步用户持有的ItemCard信息
        await syncUserItemCardsFromCloud()
    }

    /// 在用户登出时调用，清理用户相关数据
    func onUserLogout() {
        currentUserId = ""
        
        // 清除所有的本地数据
        let defaults = UserDefaults.standard
        defaults.dictionaryRepresentation().keys.forEach { key in
            defaults.removeObject(forKey: key)
        }
        defaults.synchronize() // 确认立即写入（非必须，但可确保同步）
        // 清理本地用户设置
        updateSettings { $0 = UserSetting() }
        
        // 清理目录存储
        clearDocumentsDirectory()
    }

    // MARK: - 通知监听设置

    /// 设置通知监听器
    private func setupNotificationObservers() {
        // 监听用户登录通知（基本版本）
        NotificationCenter.default.addObserver(
            forName: .userDidLogin,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self,
                  let userInfo = notification.userInfo,
                  let userId = userInfo["userId"] as? String else { return }

            Task {
                await self.onUserLogin(userId: userId)
            }
        }

        // 监听用户登录通知（携带用户数据版本）
        NotificationCenter.default.addObserver(
            forName: .userDidLoginWithData,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self,
                  let userInfo = notification.userInfo,
                  let userId = userInfo["userId"] as? String,
                  let userData = userInfo["userData"] as? UserData else { return }

            Task {
                await self.onUserLoginWithData(userId: userId, userData: userData)
            }
        }

        // 监听用户登出通知
        NotificationCenter.default.addObserver(
            forName: .userDidLogout,
            object: nil,
            queue: .main
        ) { [weak self] notification in
            guard let self = self else { return }

            Task {
                await MainActor.run {
                    self.onUserLogout()
                }
            }
        }
    }
}
